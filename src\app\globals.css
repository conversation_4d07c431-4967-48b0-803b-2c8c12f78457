/* 
 * Tailwind CSS Global Styles
 * This file includes Tailwind CSS directives and custom styles
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-roboto-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Optimized Animation Effects */
@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.7;
  }
}

@keyframes smooth-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 177, 64, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 177, 64, 0.6);
  }
}

.gentle-float {
  animation: gentle-float 6s ease-in-out infinite;
}

.smooth-glow {
  animation: smooth-glow 3s ease-in-out infinite;
}

/* 优化渐变文字效果 */
.gradient-text-hover {
  background: linear-gradient(45deg, #00B140, #00D155);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Fade in animation for components */
.fade-in {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* N8N Chat 自定义样式 - 使用CSS变量匹配PRSPARES品牌 */
:root {
  /* N8N Chat 品牌色彩 - 强制覆盖默认颜色 */
  --chat--color-primary: #00B140 !important;
  --chat--color-primary-shade-50: #00D155 !important;
  --chat--color-primary-shade-100: #009933 !important;
  --chat--color-secondary: #00D155 !important;
  --chat--color-secondary-shade-50: #00B140 !important;

  /* 聊天窗口尺寸 */
  --chat--window--width: 400px;
  --chat--window--height: 580px; /* 减小高度给输入框留出空间 */
  --chat--window--max-height: calc(100vh - 120px); /* 增加底部间距 */
  --chat--window--min-height: 400px;

  /* 聊天按钮样式 - 强制覆盖 */
  --chat--toggle--background: #00B140 !important;
  --chat--toggle--hover--background: #00D155 !important;
  --chat--toggle--active--background: #009933 !important;
  --chat--toggle--color: #ffffff !important;
  --chat--toggle--size: 64px !important;

  /* 聊天头部样式 */
  --chat--header--background: #00B140;
  --chat--header--color: #ffffff;

  /* 消息样式 */
  --chat--message--bot--background: #f8f9fa;
  --chat--message--bot--color: #333333;
  --chat--message--user--background: #00B140;
  --chat--message--user--color: #ffffff;

  /* 边框和圆角 */
  --chat--border-radius: 12px;
}

/* 确保聊天组件不会干扰页面布局 */
#n8n-chat {
  position: fixed !important;
  z-index: 9999 !important;
}

/* 额外的自定义样式 */
.n8n-chat-toggle {
  box-shadow: 0 4px 12px rgba(0, 177, 64, 0.3) !important;
  transition: all 0.3s ease !important;
}

.n8n-chat-toggle:hover {
  box-shadow: 0 6px 20px rgba(0, 177, 64, 0.4) !important;
  transform: scale(1.05) !important;
}

/* 聊天窗口基本样式 - 增加底部间距 */
.n8n-chat-window {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 177, 64, 0.1) !important;
  bottom: 40px !important; /* 进一步增加底部距离 */
  right: 20px !important;
  height: 560px !important; /* 进一步减小高度 */
  max-height: calc(100vh - 140px) !important; /* 确保不超出屏幕 */
  border-radius: 12px !important;
  overflow: hidden !important; /* 防止内容溢出 */
  display: flex !important; /* 强制flex布局 */
  flex-direction: column !important;
}

/* 强制在聊天窗口底部添加输入区域 */
.n8n-chat-window::after {
  content: '';
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 90px; /* 增加高度 */
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  border-radius: 0 0 12px 12px;
  z-index: 999;
  pointer-events: none;
}

/* 专门针对输入框容器的修复 */
.n8n-chat-window > div:last-child {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: #ffffff !important;
  border-top: 1px solid #e5e7eb !important;
  padding: 20px 15px !important; /* 增加内边距 */
  margin: 0 !important;
  z-index: 1000 !important;
  height: 90px !important; /* 增加高度 */
  min-height: 90px !important;
  max-height: 90px !important;
  box-sizing: border-box !important;
  border-radius: 0 0 12px 12px !important;
  display: flex !important;
  align-items: center !important;
}

/* N8N Chat输入框修复 */

/* 让输入区使用 flex 布局，而不是绝对定位 + calc 宽度 */
.n8n-chat-window form {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 输入框（textarea）铺满剩余空间 */
.n8n-chat-window textarea,
.n8n-chat-window input[type="text"] {
  flex: 1 1 auto !important;
  width: 100% !important;
  height: 46px !important;
  padding: 12px 16px !important;
  resize: vertical !important;
  overflow: auto !important;
  border: 1px solid #d1d5db !important;
  border-radius: 23px !important;
  font-size: 14px !important;
  background: #ffffff !important;
  color: #333333 !important;
  outline: none !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  z-index: 1001 !important;
}

.n8n-chat-window textarea:focus,
.n8n-chat-window input[type="text"]:focus {
  border-color: #00B140 !important;
  box-shadow: 0 0 0 2px rgba(0, 177, 64, 0.1) !important;
}

/* 发送按钮放在 flex 行内部，不再 absolute 定位 */
.n8n-chat-window button[type="submit"] {
  position: static !important;
  width: 46px !important;
  height: 46px !important;
  flex: 0 0 46px !important;
  margin-left: 8px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: #00B140 !important;
  border: none !important;
  color: #ffffff !important;
  cursor: pointer !important;
  z-index: 1002 !important;
}

.n8n-chat-window button[type="submit"]:hover {
  background: #00D155 !important;
  transform: scale(1.05) !important;
}

/* 确保消息区域不会覆盖输入框 */
.n8n-chat-window > div:not(:last-child) {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 15px !important;
  padding-bottom: 20px !important;
  max-height: calc(100% - 90px) !important;
}

/* 消息容器特殊处理 */
.n8n-chat-window > div:nth-child(2),
.n8n-chat-window .messages,
.n8n-chat-window .chat-messages {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 15px !important;
  padding-bottom: 20px !important;
  max-height: calc(100% - 90px) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .n8n-chat-window {
    bottom: 20px !important;
    right: 10px !important;
    left: 10px !important;
    width: calc(100vw - 20px) !important;
    max-width: none !important;
    height: calc(100vh - 40px) !important;
    max-height: calc(100vh - 40px) !important;
  }

  .n8n-chat-window input[type="text"],
  .n8n-chat-window textarea,
  .n8n-chat-window input {
    font-size: 16px !important; /* 防止iOS缩放 */
  }

  .n8n-chat-window > div:last-child {
    padding: 12px !important;
    height: 70px !important;
    min-height: 70px !important;
    max-height: 70px !important;
  }
}

/* Table of Contents Custom Scrollbar Styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Table of Contents Smooth Scrolling */
.toc-smooth-scroll {
  scroll-behavior: smooth;
}

/* Enhanced hover effects for TOC */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

/* TOC Active Item Animation */
@keyframes toc-highlight {
  0% {
    background-color: rgba(34, 197, 94, 0.1);
    transform: scale(1);
  }
  50% {
    background-color: rgba(34, 197, 94, 0.2);
    transform: scale(1.02);
  }
  100% {
    background-color: rgba(34, 197, 94, 0.1);
    transform: scale(1);
  }
}

.toc-active-highlight {
  animation: toc-highlight 0.6s ease-in-out;
}

/* Modern Hero Section Animations */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 4s ease infinite;
}

.bg-size-200 {
  background-size: 200% 200%;
}

/* Subtle hover animations */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Smooth transitions for modern UI */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}